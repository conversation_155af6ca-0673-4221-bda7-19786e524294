"use client"

import { useState, useEffect, useMemo } from "react" // Added useMemo
import { Gift, PlusCircle, Save, Trash2, Award, ChevronLeft, X, TrendingUp, HelpCircle, RefreshCw } from "lucide-react" // Added icons for roll reward chances
import { useGameRewardsDetails, RewardSet, Reward, WinMechanicsSettings, CouponSettings, GiftCardSettings, RewardType } from "@repo/shared/lib/game/useGameRewardsDetails"
import { useCampaignEditor } from "@/lib/hooks/useCampaignEditor"
import { useGameWidgets } from "@repo/shared/lib/hooks/useWidgetHelpers"

// Shadcn Chart Imports
import { Label as RechartsLabel, Pie, PieChart } from "recharts" // Renamed Label to RechartsLabel to avoid conflict
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@repo/shared/components/ui/chart"

// Assuming these paths are correct for your project structure
import { But<PERSON> } from "@repo/shared/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@repo/shared/components/ui/card"
// Dialog components are imported in campaignEditor.tsx where this component is used
import { Input } from "@repo/shared/components/ui/input"
import { Label } from "@repo/shared/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@repo/shared/components/ui/radio-group"
import { ScrollArea } from "@repo/shared/components/ui/scroll-area"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@repo/shared/components/ui/select"
import { Separator } from "@repo/shared/components/ui/separator"
import { Textarea } from "@repo/shared/components/ui/textarea"
import { Switch } from "@repo/shared/components/ui/switch"
import { Slider } from "@repo/shared/components/ui/slider"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@repo/shared/components/ui/tooltip"

// Legacy type aliases for backward compatibility (if needed)
export type UserRewardType = RewardType;
export type UserCouponRewardData = CouponSettings;
export type UserReward = Omit<Reward, 'settings'> & { data: CouponSettings | any };
export type UserRewardSet = Omit<RewardSet, 'name'> & { gameWidgetId: string };

// --- Helper for Unique IDs ---
let nextIdCounter = 0;
const generateUniqueId = (prefix: string = 'id') => {
  return `${prefix}-${Date.now()}-${nextIdCounter++}`;
};

// --- Coupon Reward Wizard (Your existing component, slightly adapted for context) ---
interface CouponRewardWizardProps {
  settings: CouponSettings;
  onChange: (newSettings: CouponSettings) => void;
}

const CouponRewardWizard: React.FC<CouponRewardWizardProps> = ({ settings, onChange }) => {
  const [newCode, setNewCode] = useState('');

  const handleAddCode = () => {
    if (newCode.trim() && !settings.codes.includes(newCode.trim())) {
      onChange({ ...settings, codes: [...settings.codes, newCode.trim()] });
      setNewCode('');
    }
  };

  const handleRemoveCode = (codeToRemove: string) => {
    onChange({ ...settings, codes: settings.codes.filter(code => code !== codeToRemove) });
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddCode();
    }
  };

  return (
    <div className="space-y-2">
      <Label htmlFor={`coupon-codes-input-${settings.codes.join('-') || 'new'}`}>Coupon Codes</Label>
      <div className="flex space-x-2">
        <Input
          id={`coupon-codes-input-${settings.codes.join('-') || 'new'}`}
          value={newCode}
          onChange={(e) => setNewCode(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder="Enter new coupon code"
          className="flex-1"
        />
        <Button onClick={handleAddCode} type="button" variant="secondary" size="sm">Add</Button>
      </div>

      {settings.codes.length > 0 ? (
        <div className="border rounded-md overflow-hidden mt-2">
          <ScrollArea className="max-h-32"> {/* Added ScrollArea for long lists */}
            <table className="w-full text-sm">
              <tbody>
                {settings.codes.map((code, index) => (
                  <tr key={code} className={index % 2 === 0 ? 'bg-muted/5' : ''}> {/* Adjusted bg color */}
                    <td className="px-3 py-1.5">{code}</td>
                    <td className="w-auto text-right pr-1"> {/* Adjusted width */}
                      <Button
                        onClick={() => handleRemoveCode(code)}
                        variant="ghost"
                        size="sm"
                        type="button"
                        className="h-7 px-2 text-destructive hover:text-destructive/80 hover:bg-destructive/10" // Matched example's destructive styling
                      >
                        Remove
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </ScrollArea>
        </div>
      ) : (
        <p className="text-xs text-muted-foreground mt-1">No coupon codes added yet.</p>
      )}
    </div>
  );
};


// Sample data is now provided by the useGameRewardsDetails hook

// --- Drop Rate Distribution Chart Component ---
interface DropRateDistributionChartProps {
  rewards: Reward[];
}

// --- Roll Reward Chances Settings Component ---
interface RollRewardChancesSettingsCardProps {
  winMechanics: WinMechanicsSettings;
  onWinMechanicsChange: (updates: Partial<WinMechanicsSettings>) => void;
  onReset: () => void;
}

const RollRewardChancesSettingsCard: React.FC<RollRewardChancesSettingsCardProps> = ({
  winMechanics,
  onWinMechanicsChange,
  onReset
}) => {
  return (
    <div className="space-y-4">
          <h3 className="text-md font-medium mt-2 px-1">Roll Reward Chances</h3>

          <div className="p-4 rounded-md border border-border bg-card">
            <div className="flex items-center space-x-2 mb-2">
              <Label className="font-medium">Chance for a reward, per attempt</Label>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger>
                    <HelpCircle size={14} className="text-muted-foreground" />
                  </TooltipTrigger>
                  <TooltipContent side="right">
                    <p className="max-w-xs">Chance that the player gets to roll for a reward from the reward pool per attempt</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
            <div className="flex items-center space-x-4 mt-2 pl-1">
              <div className="flex-1">
                <Slider
                  value={[winMechanics.baseWinChance]}
                  min={0}
                  max={100}
                  step={1}
                  onValueChange={(values) =>
                    onWinMechanicsChange({
                      baseWinChance: values[0],
                    })
                  }
                />
              </div>
              <div className="w-16">
                <Input
                  type="number"
                  min={0}
                  max={100}
                  value={winMechanics.baseWinChance}
                  onChange={(e) =>
                    onWinMechanicsChange({
                      baseWinChance: Number(e.target.value),
                    })
                  }
                  className="h-8"
                />
              </div>
              <div className="w-6 text-muted-foreground">%</div>
            </div>

            <Separator className="my-4" />

            <div className="flex items-center justify-between space-x-4 mb-4">
              <div className="flex items-center space-x-2">
                <Label htmlFor="enable-progressive" className="font-medium">
                  Progressive Roll Chance
                </Label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      <HelpCircle size={14} className="text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent side="right">
                      <p className="max-w-xs">Increase roll chance after each unsuccessful attempt</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <Switch
                id="enable-progressive"
                checked={winMechanics.enableProgressiveChance}
                onCheckedChange={(value) =>
                  onWinMechanicsChange({ enableProgressiveChance: value })
                }
              />
            </div>

            {winMechanics.enableProgressiveChance && (
              <div className="ml-6 mb-4">
                <Label htmlFor="progressive-increase" className="mb-2 block text-sm">
                  Increase Per Attempt (%)
                </Label>
                <div className="flex items-center space-x-4 mt-1">
                  <div className="flex-1">
                    <Slider
                      id="progressive-increase"
                      value={[winMechanics.progressiveChanceIncrease]}
                      min={1}
                      max={20}
                      step={1}
                      onValueChange={(values) =>
                        onWinMechanicsChange({
                          progressiveChanceIncrease: values[0],
                        })
                      }
                    />
                  </div>
                  <div className="w-16">
                    <Input
                      type="number"
                      min={1}
                      max={20}
                      value={winMechanics.progressiveChanceIncrease}
                      onChange={(e) =>
                        onWinMechanicsChange({
                          progressiveChanceIncrease: Number(e.target.value),
                        })
                      }
                      className="h-8"
                    />
                  </div>
                  <div className="w-6 text-muted-foreground">%</div>
                </div>
              </div>
            )}

            <Separator className="my-4" />

            <div className="flex items-center justify-between space-x-4">
              <div className="flex items-center space-x-2">
                <Label htmlFor="enable-guaranteed" className="font-medium">
                  Guaranteed Roll after X attempts
                </Label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      <HelpCircle size={14} className="text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent side="right">
                      <p className="max-w-xs">Force a reward roll after a specific number of attempts</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <Switch
                id="enable-guaranteed"
                checked={winMechanics.enableGuaranteedWin}
                onCheckedChange={(value) =>
                  onWinMechanicsChange({ enableGuaranteedWin: value })
                }
              />
            </div>

            {winMechanics.enableGuaranteedWin && (
              <div className="mt-4 pt-4 border-t border-border">
                <Label htmlFor="guaranteed-attempts" className="mb-2 block text-sm">
                  After X Attempts
                </Label>
                <div className="flex items-center space-x-4 mt-1">
                  <Input
                    id="guaranteed-attempts"
                    type="number"
                    value={winMechanics.guaranteedWinAttempts}
                    onChange={(e) =>
                      onWinMechanicsChange({
                        guaranteedWinAttempts: Number(e.target.value),
                      })
                    }
                    min={1}
                    className="flex-1"
                  />
                </div>
              </div>
            )}
          </div>
        </div>
  );
};

const DropRateDistributionChart: React.FC<DropRateDistributionChartProps> = ({ rewards }) => {
  const chartData = useMemo(() => {
    return rewards.filter(reward => typeof reward.dropRate === 'number').map((reward, index) => ({
      name: reward.name,
      value: reward.dropRate ,
      fill: `hsl(var(--chart-${index + 1}))`, // Dynamically assign colors
    }));
  }, [rewards]);

  const chartConfig = useMemo(() => {
    const config: ChartConfig = {};
    rewards.filter(reward => typeof reward.dropRate === 'number').forEach((reward, index) => {
      config[reward.name] = { // Use reward name as key, ensure it's a valid key
        label: reward.name,
        color: `hsl(var(--chart-${index + 1}))`,

      };
    });
    config.value = { label: "Drop Rate" }; // Generic entry for the dataKey
    return config;
  }, [rewards]);

  const totalDropRate = useMemo(() => {
    return chartData.reduce((acc, curr) => acc + curr.value, 0);
  }, [chartData]);

  const totalRewardsCount = useMemo(() => {
    return chartData.length;
  }, [chartData]);

  if (chartData.length === 0) {
    return (
      // Still show a card if there's no data, for consistency in that specific state.
      // Alternatively, this could also be a simple div with a message.
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Drop Rate Distribution</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">No rewards with drop rates defined to display.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    // Removed Card wrapper, using divs and Tailwind for structure and styling
    <div className="flex flex-col">
      <div className="p-4 pb-2"> {/* Equivalent to CardHeader styling */}
        <h3 className="text-lg font-semibold">Drop Rate Distribution</h3> {/* Equivalent to CardTitle */}
        <p className="text-sm text-muted-foreground">Visualizing reward drop probabilities</p> {/* Equivalent to CardDescription */}
      </div>
      <div className="flex-1 pb-0"> {/* Equivalent to CardContent styling */}
        <ChartContainer
          config={chartConfig}
          className="mx-auto aspect-square max-h-[250px] pb-0 [&_.recharts-pie-label-text]:fill-foreground"
        >
          <PieChart>
            <ChartTooltip
              content={<ChartTooltipContent hideLabel />}
            />
            <Pie
              data={chartData}
              dataKey="value"
              nameKey="name"
              // No label prop means no labels will be rendered
            />
          </PieChart>
        </ChartContainer>
      </div>
      <div className="flex flex-col gap-2 text-sm pt-2 pb-3 px-4 items-center text-center"> {/* Equivalent to CardFooter styling, added items-center and text-center for better standalone appearance */}
        <div className="font-medium leading-none">
          {totalRewardsCount.toLocaleString()} {totalRewardsCount === 1 ? "reward" : "rewards"} with defined drop rates
        </div>
        <div className="leading-none text-muted-foreground">
          Showing drop rates for rewards in this set.
        </div>
      </div>
    </div>
  );
};


export default function RewardsManager() {
  return <RewardsManagerContent />;
}

export function RewardsManagerContent() {
  // Use state-based reward sets for mock data
  const [rewardSets, setRewardSets] = useState<RewardSet[]>([
    {
      id: 'test',
      name: "Memory Game",
      gameWidgetId: undefined, // Will be set when user selects a game widget
      rewards: [
        {
          id: 'reward-s1-1',
          name: "20% off",
          description: "20% off on all summer products in-game.",
          type: "coupon",
          settings: { codes: ["SUMMER20", "SUNNYDEAL"] },
          dropRate: 100,
          image: {
            absoluteUrl: 'https://placehold.co/100x100/ff0000/ffffff?text=20OFF',
          },
        },
        {
          id: 'reward-2',
          name: "50% schabowy",
          description: "Special coupon for the Beach Bash event.",
          type: "coupon",
          settings: { codes: ["BEACHBASH"] },
          dropRate: 50,
          image: {
            absoluteUrl: 'https://placehold.co/100x100/ff0000/ffffff?text=50OFF',
          },
        },
      ],
      winMechanics: {
        baseWinChance: 20,
        enableProgressiveChance: false,
        progressiveChanceIncrease: 5,
        enableGuaranteedWin: true,
        guaranteedWinAttempts: 10,
      },
    },
  ])

  // Get campaign editor context and game widgets for the selector
  const { scenes } = useCampaignEditor()
  const { findGameWidgets } = useGameWidgets(scenes || [])
  const gameWidgets = findGameWidgets()

  const [selectedSetId, setSelectedSetId] = useState<string>(rewardSets[0]?.id || "")
  const [selectedRewardId, setSelectedRewardId] = useState<string>("")

  // New state for draft reward editing
  const [draftReward, setDraftReward] = useState<Reward | null>(null)
  const [isCreatingNewReward, setIsCreatingNewReward] = useState<boolean>(false)

  // Default roll reward chances settings for new reward sets
  const defaultWinMechanics: WinMechanicsSettings = {
    baseWinChance: 20,
    enableProgressiveChance: false,
    progressiveChanceIncrease: 5,
    enableGuaranteedWin: true,
    guaranteedWinAttempts: 10,
  }



  useEffect(() => {
    // Reselect first set if current selection is invalid
    if (!rewardSets.find((s: RewardSet) => s.id === selectedSetId) && rewardSets.length > 0) {
      setSelectedSetId(rewardSets[0].id);
    }
  }, [rewardSets, selectedSetId]);

  useEffect(() => {
    // Only clear the selected reward if it doesn't exist in the current set
    const currentSet = rewardSets.find(s => s.id === selectedSetId);
    if (currentSet && selectedRewardId) {
      if (!currentSet.rewards.find((r: Reward) => r.id === selectedRewardId)) {
        setSelectedRewardId("");
      }
    } else if (!currentSet) {
      setSelectedRewardId("");
    }
  }, [rewardSets, selectedSetId, selectedRewardId]);


  const selectedSet = rewardSets.find((set: RewardSet) => set.id === selectedSetId)

  // If we're editing a draft reward, use that instead of looking up the reward in the set
  const selectedReward = draftReward || selectedSet?.rewards.find((reward: Reward) => reward.id === selectedRewardId)

  const handleSelectSet = (setId: string) => {
    setSelectedSetId(setId)
    // Don't automatically select the first reward
    setSelectedRewardId("")
    // Clear any draft reward and reset creation state
    setDraftReward(null);
    setIsCreatingNewReward(false);
  }

  const handleRewardTypeChange = (newType: RewardType) => {
    if (!selectedReward) return;

    // If we're editing a draft, update the draft
    if (draftReward) {
      let newSettings: CouponSettings | GiftCardSettings;
      if (newType === "coupon") {
        newSettings = { codes: [] };
      } else { // gift_card
        newSettings = { amount: "0", currency: "USD" };
      }

      setDraftReward({
        ...draftReward,
        type: newType,
        settings: newSettings
      });
      return;
    }

    // If we're not editing a draft, create one from the selected reward
    if (selectedSet && selectedRewardId) {
      const rewardToEdit = selectedSet.rewards.find(r => r.id === selectedRewardId);
      if (rewardToEdit) {
        let newSettings: CouponSettings | GiftCardSettings;
        if (newType === "coupon") {
          newSettings = { codes: [] };
        } else { // gift_card
          newSettings = { amount: "0", currency: "USD" };
        }

        setDraftReward({
          ...rewardToEdit,
          type: newType,
          settings: newSettings
        });
      }
    }
  };

  const handleInputChange = (field: string, value: string) => {
    if (!selectedReward) return;

    // If we're editing a draft, update the draft
    if (draftReward) {
      if (field === "name" || field === "description") {
        setDraftReward({
          ...draftReward,
          [field]: value
        });
      } else if (field === "dropRate") {
        setDraftReward({
          ...draftReward,
          dropRate: value === '' ? undefined : Number(value)
        });
      } else {
        const [settingCategory, settingField] = field.split(".");
        if (settingCategory === "settings" && settingField && draftReward.settings) {
          setDraftReward({
            ...draftReward,
            settings: {
              ...(draftReward.settings as any), // Cast to any to allow dynamic key
              [settingField]: value,
            },
          });
        }
      }
      return;
    }

    // If we're not editing a draft, create one from the selected reward
    if (selectedSet && selectedRewardId) {
      const rewardToEdit = selectedSet.rewards.find(r => r.id === selectedRewardId);
      if (rewardToEdit) {
        let updatedReward = { ...rewardToEdit };

        if (field === "name" || field === "description") {
          updatedReward = { ...updatedReward, [field]: value };
        } else if (field === "dropRate") {
          updatedReward = { ...updatedReward, dropRate: value === '' ? undefined : Number(value) };
        }
        else {
          const [settingCategory, settingField] = field.split(".");
          if (settingCategory === "settings" && settingField && updatedReward.settings) {
            updatedReward = {
              ...updatedReward,
              settings: {
                ...(updatedReward.settings as any), // Cast to any to allow dynamic key
                [settingField]: value,
              },
            };
          }
        }
        setDraftReward(updatedReward);
      }
    }
  };

  const handleCouponSettingsChange = (newCouponSettings: CouponSettings) => {
    if (!selectedReward || selectedReward.type !== 'coupon') return;

    // If we're editing a draft, update the draft
    if (draftReward) {
      setDraftReward({
        ...draftReward,
        settings: newCouponSettings
      });
      return;
    }

    // If we're not editing a draft, create one from the selected reward
    if (selectedSet && selectedRewardId) {
      const rewardToEdit = selectedSet.rewards.find(r => r.id === selectedRewardId);
      if (rewardToEdit) {
        setDraftReward({
          ...rewardToEdit,
          settings: newCouponSettings
        });
      }
    }
  };

  const handleAddRewardSet = () => {
    const newSet: RewardSet = {
      id: generateUniqueId('set'),
      name: 'New Reward Set',
      gameWidgetId: undefined, // Will be set when user selects a game widget
      rewards: [],
      winMechanics: { ...defaultWinMechanics },
    };
    setRewardSets((prevSets: RewardSet[]) => [...prevSets, newSet]);
    setSelectedSetId(newSet.id);
    setSelectedRewardId("");
  };

  const handleRollRewardChancesChange = (updates: Partial<WinMechanicsSettings>) => {
    if (!selectedSetId) return;

    setRewardSets(prevSets =>
      prevSets.map(set => {
        if (set.id === selectedSetId) {
          return {
            ...set,
            winMechanics: {
              ...set.winMechanics,
              ...updates
            }
          };
        }
        return set;
      })
    );
  };

  const handleResetRollRewardChances = () => {
    if (!selectedSetId) return;

    setRewardSets(prevSets =>
      prevSets.map(set => {
        if (set.id === selectedSetId) {
          return {
            ...set,
            winMechanics: { ...defaultWinMechanics }
          };
        }
        return set;
      })
    );
  };

  const handleAddRewardToSet = (currentSetId: string) => {
    // Create a draft reward but don't add it to the list yet
    const defaultNewReward: Reward = {
      id: generateUniqueId('reward'),
      name: 'New Reward',
      description: '',
      type: 'coupon', // Default new rewards to coupon type
      settings: { codes: [] } as CouponSettings,
      dropRate: 100, // Default dropRate to 100
    };

    // Set the draft reward and mark that we're creating a new reward
    setDraftReward(defaultNewReward);
    setIsCreatingNewReward(true);

    // We're editing a reward for the current set
    setSelectedSetId(currentSetId);
  };

  const handleDeleteReward = () => {
    if (isCreatingNewReward) {
      // If we're creating a new reward, just cancel the creation
      handleCancelEdit();
      return;
    }

    if (!selectedSetId || !selectedRewardId) return;

    setRewardSets(prevSets =>
      prevSets.map(set => {
        if (set.id === selectedSetId) {
          return { ...set, rewards: set.rewards.filter((r: Reward) => r.id !== selectedRewardId) };
        }
        return set;
      })
    );
    // After deletion, selectedRewardId might become invalid, useEffect will handle re-selection.
  };

  // Function to save the draft reward to the actual reward set
  const handleSaveReward = () => {
    // If there's a draft reward, save it
    if (draftReward && selectedSetId) {
      if (isCreatingNewReward) {
        // Add the new reward to the set
        setRewardSets(prevSets =>
          prevSets.map(set => {
            if (set.id === selectedSetId) {
              return { ...set, rewards: [...set.rewards, draftReward] };
            }
            return set;
          })
        );
      } else {
        // Update an existing reward
        setRewardSets(prevSets =>
          prevSets.map(set => {
            if (set.id === selectedSetId) {
              return {
                ...set,
                rewards: set.rewards.map(r =>
                  r.id === draftReward.id ? draftReward : r
                )
              };
            }
            return set;
          })
        );
      }
    }

    // Always clear the draft and go back to reward set details view, even if no changes were made
    setDraftReward(null);
    setIsCreatingNewReward(false);
    setSelectedRewardId(""); // Clear selected reward ID to go back to reward set details
  };

  // Function to cancel editing and discard changes
  const handleCancelEdit = () => {
    setDraftReward(null);
    setIsCreatingNewReward(false);

    // If we were creating a new reward, go back to the reward set view
    if (isCreatingNewReward) {
      setSelectedRewardId("");
    }
  };


  return (
    <TooltipProvider>
      <div className="flex flex-col h-full">
      <div className="flex flex-1 h-full overflow-hidden">
        <div className="w-full max-w-[250px] border-r bg-muted/40">
          <div className="p-4 flex justify-between items-center">
            <h2 className="font-semibold">Reward Sets</h2>
            <Button
              onClick={handleAddRewardSet}
              size="sm"
              variant="outline"
              className="h-6 w-6 p-0"
            >
              <PlusCircle className="h-3 w-3" />
            </Button>
          </div>
          <ScrollArea className="h-[calc(100%-4rem)]">
            <div className="px-1">
              {rewardSets.map((set) => (
                <div key={set.id} className="mb-4">
                  <button
                    onClick={() => handleSelectSet(set.id)}
                    className={`w-full text-left px-3 py-2 rounded-md transition-colors ${
                      selectedSetId === set.id ? "bg-accent text-accent-foreground" : "hover:bg-muted"
                    }`}
                  >
                    <div className="font-medium">{set.name}</div>
                    <div className="text-xs text-muted-foreground">{set.rewards.length} reward{set.rewards.length !==1 ? 's':''}</div>
                  </button>
                </div>
              ))}
            </div>
          </ScrollArea>
        </div>

        {/* Main content */}
        <div className="flex-1 h-full"> {/* Removed overflow-auto */}
          <ScrollArea className="h-full max-h-[1000px] w-full"> {/* Added ScrollArea with max-height */}
            {selectedReward ? (
              <div className="p-8 space-y-6"> {/* Increased padding from p-6 to p-8 */}
                <div className="flex items-center"> {/* Header section */}
                <Button
                  variant="ghost"
                  size="sm"
                  className="mr-2 h-8 w-8 p-0"
                  onClick={() => draftReward ? handleCancelEdit() : setSelectedRewardId("")}
                >
                  <ChevronLeft className="h-4 w-4" />
                  <span className="sr-only">Back to reward set</span>
                </Button>
                <div>
                  <h2 className="text-2xl font-semibold tracking-tight">Reward Settings</h2>
                  <p className="text-sm text-muted-foreground">Configure the selected reward: {selectedReward.name}</p>
                </div>
              </div>
              {/* Removed CardHeader and CardContent, content directly inside the p-6 div */}
              {/* General Settings */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">General Information</h3>
                    <div className="grid gap-4">
                      <div className="grid gap-2">
                        <Label htmlFor="reward-name">Reward Name</Label>
                        <Input
                          id="reward-name"
                          value={selectedReward.name}
                          onChange={(e) => handleInputChange("name", e.target.value)}
                        />
                      </div>
                      <div className="grid gap-2">
                        <Label htmlFor="reward-description">Description</Label>
                        <Textarea
                          id="reward-description"
                          value={selectedReward.description}
                          onChange={(e) => handleInputChange("description", e.target.value)}
                        />
                      </div>
                      <div className="grid gap-2">
                        <Label htmlFor="reward-drop-rate">Drop Rate (%)</Label>
                        <Input
                          id="reward-drop-rate"
                          type="number"
                          min="0"
                          max="100"
                          value={selectedReward.dropRate === undefined ? '' : String(selectedReward.dropRate)}
                          onChange={(e) => handleInputChange("dropRate", e.target.value)}
                          placeholder="0-100"
                        />
                      </div>
                    </div>
                  </div>

                  <Separator />

                  {/* Reward Type */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Reward Type</h3>
                    <RadioGroup
                      value={selectedReward.type}
                      onValueChange={(value) => handleRewardTypeChange(value as RewardType)}
                      className="grid grid-cols-2 gap-4"
                    >
                      <div className="flex items-center space-x-2 rounded-md border p-4 has-[input:checked]:border-primary">
                        <RadioGroupItem value="coupon" id="type-coupon" />
                        <Label htmlFor="type-coupon" className="flex items-center gap-2 cursor-pointer">
                          <Gift className="h-5 w-5" />
                          Coupon Code
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2 rounded-md border p-4 has-[input:checked]:border-primary">
                        <RadioGroupItem value="gift_card" id="type-gift_card" />
                        <Label htmlFor="type-gift_card" className="flex items-center gap-2 cursor-pointer">
                          <Award className="h-5 w-5" /> {/* Using Award for gift_card */}
                          Gift Card
                        </Label>
                      </div>
                    </RadioGroup>
                  </div>

                  <Separator />

                  {/* Type-specific settings */}
                  {selectedReward.type === "coupon" ? (
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium">Coupon Settings</h3>
                      <CouponRewardWizard
                        settings={selectedReward.settings as CouponSettings}
                        onChange={handleCouponSettingsChange}
                      />
                    </div>
                  ) : selectedReward.type === "gift_card" ? (
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium">Gift Card Settings</h3>
                      <div className="grid gap-4">
                        <div className="grid gap-2">
                          <Label htmlFor="giftcard-amount">Amount</Label>
                          <Input
                            id="giftcard-amount"
                            type="number"
                            value={(selectedReward.settings as GiftCardSettings).amount || ""}
                            onChange={(e) => handleInputChange("settings.amount", e.target.value)}
                            placeholder="e.g., 10.00"
                          />
                        </div>
                        <div className="grid gap-2">
                          <Label htmlFor="giftcard-currency">Currency</Label>
                           <Select
                              value={(selectedReward.settings as GiftCardSettings).currency || "USD"}
                              onValueChange={(value) => handleInputChange("settings.currency", value)}
                            >
                              <SelectTrigger id="giftcard-currency">
                                <SelectValue placeholder="Select currency" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="USD">USD</SelectItem>
                                <SelectItem value="EUR">EUR</SelectItem>
                                <SelectItem value="GBP">GBP</SelectItem>
                                <SelectItem value="CAD">CAD</SelectItem>
                              </SelectContent>
                            </Select>
                        </div>
                      </div>
                    </div>
                  ) : null}
                {/* Removed CardContent */}
              <div className="flex justify-between pt-6 border-t"> {/* Moved CardFooter content here, added border-t for separation */}
                <Button variant="outline" size="sm" className="text-destructive hover:bg-destructive/10" onClick={handleDeleteReward}>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Reward
                </Button>
                <div className="flex gap-2">
                  <Button onClick={handleSaveReward}>
                    <Save className="h-4 w-4 mr-2" />
                    Save Changes
                  </Button>
                </div>
              </div>
            </div>
          ) : selectedSetId ? (
            <div className="p-8 space-y-6">  {/* Increased padding from p-6 to p-8 */}
           

              {selectedSet && (
                <div className="space-y-4 p-4 border rounded-lg bg-muted/20">
                  <div className="flex items-center gap-2">
                    <h3 className="text-lg font-medium">Game Widget Binding</h3>
                    <Tooltip>
                      <TooltipTrigger>
                        <HelpCircle className="h-4 w-4 text-muted-foreground" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Bind this reward set to a specific game widget for automatic reward distribution</p>
                      </TooltipContent>
                    </Tooltip>
                  </div>
                  <div className="space-y-2">
                    <Select
                      value={selectedSet.gameWidgetId || ""}
                      onValueChange={(value) => {
                        setRewardSets((prevSets: RewardSet[]) =>
                          prevSets.map((set: RewardSet) => {
                            if (set.id === selectedSetId) {
                              return { ...set, gameWidgetId: value };
                            }
                            return set;
                          })
                        );
                      }}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select game widget to bind rewards" />
                      </SelectTrigger>
                      <SelectContent>
                        {gameWidgets.map((widget) => (
                          <SelectItem key={widget.widgetId} value={widget.widgetId}>
                            {widget.gameId} ({widget.sceneName || 'Scene'})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                
                  </div>
                </div>
              )}

              {/* Roll Reward Chances Settings - Added at the top */}
              {selectedSet && (
                <RollRewardChancesSettingsCard
                  winMechanics={selectedSet.winMechanics}
                  onWinMechanicsChange={handleRollRewardChancesChange}
                  onReset={handleResetRollRewardChances}
                />
              )}

              {/* Rewards Table Section - MOVED UP */}
              <div className="flex justify-between items-center mt-6"> {/* Added margin-top */}
                <h3 className="text-lg font-medium">Rewards List</h3>
                <Button
                  onClick={() => handleAddRewardToSet(selectedSetId)}
                  size="sm"
                >
                  <PlusCircle className="h-4 w-4 mr-2" />
                  Add Reward
                </Button>
              </div>

              {selectedSet && selectedSet.rewards.length > 0 ? (
                    <div className="border rounded-md overflow-hidden">
                      <ScrollArea className="max-h-[400px]">
                        <table className="w-full text-sm">
                          <thead className="bg-muted/50 border-b">
                            <tr>
                              <th className="px-4 py-3 text-left font-medium">Type</th>
                              <th className="px-4 py-3 text-left font-medium">Name</th>
                              <th className="px-4 py-3 text-left font-medium">Description</th>
                              <th className="px-4 py-3 text-left font-medium">Drop Rate</th>
                              <th className="px-4 py-3 text-right font-medium">Delete</th>
                            </tr>
                          </thead>
                          <tbody>
                            {selectedSet.rewards.map((reward, index) => (
                              <tr
                                key={reward.id}
                                className={`${index % 2 === 0 ? 'bg-muted/5' : ''} cursor-pointer hover:bg-muted/20`}
                                onClick={() => setSelectedRewardId(reward.id)}
                              >
                                <td className="px-4 py-3">
                                  {reward.type === "coupon" ? (
                                    <div className="flex items-center gap-2">
                                      <Gift className="h-4 w-4 text-muted-foreground" />
                                      <span>Coupon</span>
                                    </div>
                                  ) : (
                                    <div className="flex items-center gap-2">
                                      <Award className="h-4 w-4 text-muted-foreground" />
                                      <span>Gift Card</span>
                                    </div>
                                  )}
                                </td>
                                <td className="px-4 py-3">{reward.name}</td>
                                <td className="px-4 py-3 max-w-[300px] truncate">{reward.description}</td>
                                <td className="px-4 py-3">{reward.dropRate !== undefined ? `${reward.dropRate}%` : '-'}</td>
                                <td className="px-4 py-3 text-right">
                                  <Button
                                    onClick={(e) => {
                                      e.stopPropagation(); // Prevent row click
                                      const confirmDelete = window.confirm(`Are you sure you want to delete "${reward.name}"?`);
                                      if (confirmDelete) {
                                        // Create a temporary variable to store the ID before deleting
                                        const rewardIdToDelete = reward.id;
                                        // Update the rewards list by filtering out the deleted reward
                                        setRewardSets(prevSets =>
                                          prevSets.map(set => {
                                            if (set.id === selectedSetId) {
                                              return { ...set, rewards: set.rewards.filter(r => r.id !== rewardIdToDelete) };
                                            }
                                            return set;
                                          })
                                        );
                                      }
                                    }}
                                    variant="ghost"
                                    size="sm"
                                    className="text-destructive hover:text-destructive/80 hover:bg-destructive/10"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                    <span className="sr-only">Delete</span>
                                  </Button>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </ScrollArea>
                    </div>
                  ) : (
                    <div className="text-center p-8 border rounded-md bg-muted/5">
                      <Gift className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <h4 className="text-lg font-medium mb-2">No rewards yet</h4>
                      <p className="text-sm text-muted-foreground mb-4">
                        This reward set doesn't have any rewards. Add your first reward to get started.
                      </p>
                      <Button onClick={() => handleAddRewardToSet(selectedSetId)}>
                        <PlusCircle className="h-4 w-4 mr-2" />
                        Add First Reward
                      </Button>
                    </div>
                  )}

              <Separator /> {/* Optional: Add a separator if needed */}

              {/* Drop Rate Chart - MOVED DOWN */}
              {selectedSet && selectedSet.rewards && selectedSet.rewards.some(r => typeof r.dropRate === 'number') && (
                <div className="my-6"> {/* Added margin for spacing */}
                  <DropRateDistributionChart rewards={selectedSet.rewards} />
                </div>
              )}
                {/* End of removed CardContent */}
              </div>
            ) : (
              <div className="h-full flex items-center justify-center">
                <div className="text-center text-muted-foreground p-10">
                   <Gift className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <h3 className="text-lg font-medium">
                    No reward set selected
                  </h3>
                  <p className="text-sm">
                    Select a reward set from the sidebar, or create a new one.
                  </p>
                </div>
              </div>
            )}
          </ScrollArea>
        </div>
      </div>
    </div>
    </TooltipProvider>
  )
}
