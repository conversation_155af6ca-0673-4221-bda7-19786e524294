import { useGameAtom } from '../atoms/atom-extensions'
import { AssetUrl } from '../types/widgetSettings';

// Types for reward sets and rewards
export type WinMechanicsMode = "attempts" | "score";

export interface WinMechanicsSettings {
  mode: WinMechanicsMode;
  baseWinChance: number;
  enableProgressiveChance: boolean;
  progressiveChanceIncrease: number;
  enableGuaranteedWin: boolean;
  guaranteedWinAttempts: number;
  // Score-based settings
  scoreThreshold?: number;
  enableScoreMultiplier?: boolean;
  scoreMultiplierFactor?: number;
  guaranteedWinScore?: number;
}

export interface CouponSettings {
  codes: string[];
}

export interface GiftCardSettings {
  amount: string;
  currency: string;
}

export type RewardType = "coupon" | "gift_card";

export interface Reward {
  id: string;
  name: string;
  description: string;
  image?: AssetUrl
  type: RewardType;
  settings: CouponSettings | GiftCardSettings;
  dropRate?: number;
}

export interface RewardSet {
  id: string;
  name: string;
  gameWidgetId?: string;
  rewards: Reward[];
  winMechanics: WinMechanicsSettings;
}

// Sample data for initial rewards - this will be replaced with API call later
const initialRewardSets: RewardSet[] = [
  {
    id: `test`,
    name: "Memory Game",
    rewards: [
      {
        id: `reward-s1-1`,
        name: "20% off",
        description: "20% off on all summer products in-game.",
        type: "coupon",
        settings: { codes: ["SUMMER20", "SUNNYDEAL"] },
        dropRate: 100,
        image: {
          absoluteUrl: 'https://placehold.co/100x100/ff0000/ffffff?text=20OFF',
        },
      },
      {
        id: `reward-2`,
        name: "50% schabowy",
        description: "Special coupon for the Beach Bash event.",
        type: "coupon",
        settings: { codes: ["BEACHBASH"] },
        dropRate: 50,
        image: {
          absoluteUrl: 'https://placehold.co/100x100/ff0000/ffffff?text=50OFF',
        },
       
      },
      {
        id: `reward-3`,
        name: "100% off",
        description: "100% off on all products in-game.",
        type: "coupon",
        settings: { codes: ["FREEGAMES"] },
        dropRate: 10,
        image: {
          absoluteUrl: 'https://placehold.co/100x100/ff0000/ffffff?text=100OFF',
        },
      },
      {
        id: `reward-5`,
        name: "69% off",
        description: "69% off on all products in-game.",
        type: "coupon",
        settings: { codes: ["FREEGAMES"] },
        dropRate: 10,
        image: {
          absoluteUrl: 'https://placehold.co/100x100/ff0000/ffffff?text=69OFF',
        },
      }
    ],
    winMechanics: {
      mode: "attempts",
      baseWinChance: 30,
      enableProgressiveChance: true,
      progressiveChanceIncrease: 5,
      enableGuaranteedWin: true,
      guaranteedWinAttempts: 10,
      scoreThreshold: 1000,
      enableScoreMultiplier: false,
      scoreMultiplierFactor: 1.5,
      guaranteedWinScore: 5000,
    },
  }
]


export const useGameRewardsDetails = (gameWidgetId: string) => {
  // Use gameAtom to store the reward sets with the game widget ID
  const [rewardSets, setRewardSets] = useGameAtom<RewardSet[]>(
    gameWidgetId,
    'rewardSets',
    initialRewardSets
  );

  return {
    rewardSets,
    setRewardSets
  };
};

export const useRewardSet = (gameWidgetId: string) => {
  const { rewardSets } = useGameRewardsDetails(gameWidgetId)
  return rewardSets[0]
}